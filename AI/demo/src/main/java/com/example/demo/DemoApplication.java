package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DemoApplication.class, args);
		System.out.println("\n" +
			"=================================================================\n" +
			"  Warehouse Management System Started Successfully!\n" +
			"=================================================================\n" +
			"  Application URL: http://localhost:8080\n" +
			"  Swagger UI: http://localhost:8080/swagger-ui.html\n" +
			"  H2 Console: http://localhost:8080/h2-console\n" +
			"  \n" +
			"  Default Users:\n" +
			"  - Admin: admin/admin123\n" +
			"  - User: user/user123\n" +
			"  \n" +
			"  API Endpoints:\n" +
			"  - Authentication: /api/auth/*\n" +
			"  - Admin Operations: /api/admin/*\n" +
			"  - User Operations: /api/user/*\n" +
			"=================================================================\n");
	}

}
