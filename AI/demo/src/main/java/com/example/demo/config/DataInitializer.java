package com.example.demo.config;

import com.example.demo.entity.Product;
import com.example.demo.entity.User;
import com.example.demo.entity.DeliveryTracking;
import com.example.demo.repository.ProductRepository;
import com.example.demo.repository.UserRepository;
import com.example.demo.repository.DeliveryTrackingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private DeliveryTrackingRepository deliveryTrackingRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initializeUsers();
        initializeProducts();
        initializeDeliveries();
    }

    private void initializeUsers() {
        // Create default admin user
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("Admin");
            admin.setLastName("User");
            admin.setRole(User.Role.ADMIN);
            admin.setEnabled(true);
            userRepository.save(admin);
            System.out.println("Default admin user created: admin/admin123");
        }

        // Create default normal user
        if (!userRepository.existsByUsername("user")) {
            User user = new User();
            user.setUsername("user");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("user123"));
            user.setFirstName("Normal");
            user.setLastName("User");
            user.setRole(User.Role.USER);
            user.setEnabled(true);
            userRepository.save(user);
            System.out.println("Default normal user created: user/user123");
        }

        // Create additional sample users
        if (!userRepository.existsByUsername("john.doe")) {
            User john = new User();
            john.setUsername("john.doe");
            john.setEmail("<EMAIL>");
            john.setPassword(passwordEncoder.encode("password123"));
            john.setFirstName("John");
            john.setLastName("Doe");
            john.setRole(User.Role.USER);
            john.setEnabled(true);
            userRepository.save(john);
        }

        if (!userRepository.existsByUsername("jane.smith")) {
            User jane = new User();
            jane.setUsername("jane.smith");
            jane.setEmail("<EMAIL>");
            jane.setPassword(passwordEncoder.encode("password123"));
            jane.setFirstName("Jane");
            jane.setLastName("Smith");
            jane.setRole(User.Role.USER);
            jane.setEnabled(true);
            userRepository.save(jane);
        }
    }

    private void initializeProducts() {
        if (productRepository.count() == 0) {
            // Electronics
            Product laptop = new Product();
            laptop.setName("Dell Laptop XPS 13");
            laptop.setDescription("High-performance ultrabook with 13-inch display");
            laptop.setSku("DELL-XPS13-001");
            laptop.setPrice(new BigDecimal("1299.99"));
            laptop.setQuantity(25);
            laptop.setWeight(new BigDecimal("1.2"));
            laptop.setDimensions("30.2 x 19.9 x 1.4 cm");
            laptop.setCategory("Electronics");
            laptop.setStatus(Product.ProductStatus.AVAILABLE);
            laptop.setWarehouseLocation("A1-B2");
            laptop.setCreatedBy("admin");
            productRepository.save(laptop);

            Product smartphone = new Product();
            smartphone.setName("iPhone 14 Pro");
            smartphone.setDescription("Latest iPhone with advanced camera system");
            smartphone.setSku("APPLE-IP14P-001");
            smartphone.setPrice(new BigDecimal("999.99"));
            smartphone.setQuantity(50);
            smartphone.setWeight(new BigDecimal("0.206"));
            smartphone.setDimensions("14.75 x 7.15 x 0.79 cm");
            smartphone.setCategory("Electronics");
            smartphone.setStatus(Product.ProductStatus.AVAILABLE);
            smartphone.setWarehouseLocation("A2-C1");
            smartphone.setCreatedBy("admin");
            productRepository.save(smartphone);

            // Clothing
            Product tshirt = new Product();
            tshirt.setName("Cotton T-Shirt");
            tshirt.setDescription("100% cotton comfortable t-shirt");
            tshirt.setSku("CLOTH-TSH-001");
            tshirt.setPrice(new BigDecimal("19.99"));
            tshirt.setQuantity(100);
            tshirt.setWeight(new BigDecimal("0.2"));
            tshirt.setDimensions("Medium size");
            tshirt.setCategory("Clothing");
            tshirt.setStatus(Product.ProductStatus.AVAILABLE);
            tshirt.setWarehouseLocation("B1-A3");
            tshirt.setCreatedBy("admin");
            productRepository.save(tshirt);

            // Books
            Product book = new Product();
            book.setName("Spring Boot in Action");
            book.setDescription("Comprehensive guide to Spring Boot development");
            book.setSku("BOOK-SB-001");
            book.setPrice(new BigDecimal("39.99"));
            book.setQuantity(30);
            book.setWeight(new BigDecimal("0.5"));
            book.setDimensions("23 x 18 x 2 cm");
            book.setCategory("Books");
            book.setStatus(Product.ProductStatus.AVAILABLE);
            book.setWarehouseLocation("C1-D2");
            book.setCreatedBy("admin");
            productRepository.save(book);

            // Home & Garden
            Product chair = new Product();
            chair.setName("Office Chair");
            chair.setDescription("Ergonomic office chair with lumbar support");
            chair.setSku("FURN-CHAIR-001");
            chair.setPrice(new BigDecimal("199.99"));
            chair.setQuantity(15);
            chair.setWeight(new BigDecimal("12.5"));
            chair.setDimensions("60 x 60 x 110 cm");
            chair.setCategory("Furniture");
            chair.setStatus(Product.ProductStatus.AVAILABLE);
            chair.setWarehouseLocation("D1-E1");
            chair.setCreatedBy("admin");
            productRepository.save(chair);

            // Low stock item
            Product headphones = new Product();
            headphones.setName("Wireless Headphones");
            headphones.setDescription("Noise-cancelling wireless headphones");
            headphones.setSku("AUDIO-WH-001");
            headphones.setPrice(new BigDecimal("149.99"));
            headphones.setQuantity(5); // Low stock
            headphones.setWeight(new BigDecimal("0.3"));
            headphones.setDimensions("20 x 18 x 8 cm");
            headphones.setCategory("Electronics");
            headphones.setStatus(Product.ProductStatus.AVAILABLE);
            headphones.setWarehouseLocation("A3-B1");
            headphones.setCreatedBy("admin");
            productRepository.save(headphones);

            System.out.println("Sample products created successfully");
        }
    }

    private void initializeDeliveries() {
        if (deliveryTrackingRepository.count() == 0 && productRepository.count() > 0) {
            Product laptop = productRepository.findBySku("DELL-XPS13-001").orElse(null);
            Product smartphone = productRepository.findBySku("APPLE-IP14P-001").orElse(null);

            if (laptop != null) {
                DeliveryTracking delivery1 = new DeliveryTracking();
                delivery1.setOrderId("ORD-2024-001");
                delivery1.setProduct(laptop);
                delivery1.setQuantity(1);
                delivery1.setStatus(DeliveryTracking.DeliveryStatus.PROCESSING);
                delivery1.setCustomerName("Alice Johnson");
                delivery1.setCustomerEmail("<EMAIL>");
                delivery1.setCustomerPhone("******-0123");
                delivery1.setShippingAddress("123 Main St, Anytown, ST 12345, USA");
                delivery1.setEstimatedDelivery(LocalDateTime.now().plusDays(3));
                delivery1.setAssignedTo("user");
                delivery1.setNotes("Handle with care - fragile electronics");
                deliveryTrackingRepository.save(delivery1);
            }

            if (smartphone != null) {
                DeliveryTracking delivery2 = new DeliveryTracking();
                delivery2.setOrderId("ORD-2024-002");
                delivery2.setProduct(smartphone);
                delivery2.setQuantity(2);
                delivery2.setStatus(DeliveryTracking.DeliveryStatus.PACKED);
                delivery2.setCustomerName("Bob Smith");
                delivery2.setCustomerEmail("<EMAIL>");
                delivery2.setCustomerPhone("******-0456");
                delivery2.setShippingAddress("456 Oak Ave, Another City, ST 67890, USA");
                delivery2.setTrackingNumber("TRK123456789");
                delivery2.setCarrierName("FastShip Express");
                delivery2.setEstimatedDelivery(LocalDateTime.now().plusDays(2));
                delivery2.setAssignedTo("john.doe");
                delivery2.setNotes("Express delivery requested");
                deliveryTrackingRepository.save(delivery2);
            }

            System.out.println("Sample deliveries created successfully");
        }
    }
}
