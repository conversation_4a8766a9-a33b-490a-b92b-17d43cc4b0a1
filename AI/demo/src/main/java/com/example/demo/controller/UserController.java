package com.example.demo.controller;

import com.example.demo.entity.DeliveryTracking;
import com.example.demo.entity.Product;
import com.example.demo.service.DeliveryTrackingService;
import com.example.demo.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/user")
@PreAuthorize("hasAnyRole('ADMIN', 'USER')")
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "User", description = "User operations for delivery tracking and warehouse management")
public class UserController {

    @Autowired
    private DeliveryTrackingService deliveryTrackingService;

    @Autowired
    private ProductService productService;

    @GetMapping("/deliveries")
    @Operation(summary = "Get user's deliveries", description = "Get all deliveries assigned to the current user")
    public ResponseEntity<List<DeliveryTracking>> getUserDeliveries(Authentication authentication) {
        String username = authentication.getName();
        List<DeliveryTracking> deliveries = deliveryTrackingService.getDeliveriesAssignedTo(username);
        return ResponseEntity.ok(deliveries);
    }

    @GetMapping("/deliveries/{id}")
    @Operation(summary = "Get delivery by ID", description = "Get a specific delivery by its ID")
    public ResponseEntity<?> getDeliveryById(@PathVariable Long id) {
        return deliveryTrackingService.getDeliveryById(id)
                .map(delivery -> ResponseEntity.ok().body(delivery))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/deliveries/order/{orderId}")
    @Operation(summary = "Get delivery by order ID", description = "Get delivery information by order ID")
    public ResponseEntity<?> getDeliveryByOrderId(@PathVariable String orderId) {
        return deliveryTrackingService.getDeliveryByOrderId(orderId)
                .map(delivery -> ResponseEntity.ok().body(delivery))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/deliveries/tracking/{trackingNumber}")
    @Operation(summary = "Track delivery", description = "Track delivery by tracking number")
    public ResponseEntity<?> trackDelivery(@PathVariable String trackingNumber) {
        return deliveryTrackingService.getDeliveryByTrackingNumber(trackingNumber)
                .map(delivery -> ResponseEntity.ok().body(delivery))
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/deliveries")
    @Operation(summary = "Create new delivery", description = "Create a new delivery tracking entry")
    public ResponseEntity<?> createDelivery(@Valid @RequestBody DeliveryTracking delivery, Authentication authentication) {
        try {
            String username = authentication.getName();
            DeliveryTracking createdDelivery = deliveryTrackingService.createDelivery(delivery, username);
            return ResponseEntity.ok(createdDelivery);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to create delivery: " + e.getMessage());
        }
    }

    @PutMapping("/deliveries/{id}/status")
    @Operation(summary = "Update delivery status", description = "Update the status of a delivery")
    public ResponseEntity<?> updateDeliveryStatus(@PathVariable Long id, 
                                                 @RequestBody Map<String, String> request,
                                                 Authentication authentication) {
        try {
            String statusStr = request.get("status");
            DeliveryTracking.DeliveryStatus status = DeliveryTracking.DeliveryStatus.valueOf(statusStr.toUpperCase());
            String username = authentication.getName();
            
            DeliveryTracking updatedDelivery = deliveryTrackingService.updateDeliveryStatus(id, status, username);
            return ResponseEntity.ok(updatedDelivery);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to update delivery status: " + e.getMessage());
        }
    }

    @PutMapping("/deliveries/{id}/pack")
    @Operation(summary = "Mark delivery as packed", description = "Mark a delivery as packed and ready for shipping")
    public ResponseEntity<?> packDelivery(@PathVariable Long id, Authentication authentication) {
        try {
            String username = authentication.getName();
            DeliveryTracking updatedDelivery = deliveryTrackingService.updateDeliveryStatus(
                id, DeliveryTracking.DeliveryStatus.PACKED, username);
            return ResponseEntity.ok(updatedDelivery);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to pack delivery: " + e.getMessage());
        }
    }

    @PutMapping("/deliveries/{id}/ship")
    @Operation(summary = "Mark delivery as shipped", description = "Mark a delivery as shipped")
    public ResponseEntity<?> shipDelivery(@PathVariable Long id, Authentication authentication) {
        try {
            String username = authentication.getName();
            DeliveryTracking updatedDelivery = deliveryTrackingService.updateDeliveryStatus(
                id, DeliveryTracking.DeliveryStatus.SHIPPED, username);
            
            // Generate tracking number if not exists
            if (updatedDelivery.getTrackingNumber() == null || updatedDelivery.getTrackingNumber().isEmpty()) {
                updatedDelivery = deliveryTrackingService.generateTrackingNumber(id);
            }
            
            return ResponseEntity.ok(updatedDelivery);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to ship delivery: " + e.getMessage());
        }
    }

    @PutMapping("/deliveries/{id}/deliver")
    @Operation(summary = "Mark delivery as delivered", description = "Mark a delivery as delivered")
    public ResponseEntity<?> deliverDelivery(@PathVariable Long id, Authentication authentication) {
        try {
            String username = authentication.getName();
            DeliveryTracking updatedDelivery = deliveryTrackingService.updateDeliveryStatus(
                id, DeliveryTracking.DeliveryStatus.DELIVERED, username);
            return ResponseEntity.ok(updatedDelivery);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to mark delivery as delivered: " + e.getMessage());
        }
    }

    @GetMapping("/deliveries/search")
    @Operation(summary = "Search deliveries", description = "Search deliveries by order ID, tracking number, or customer name")
    public ResponseEntity<List<DeliveryTracking>> searchDeliveries(@RequestParam String query) {
        List<DeliveryTracking> deliveries = deliveryTrackingService.searchDeliveries(query);
        return ResponseEntity.ok(deliveries);
    }

    @GetMapping("/deliveries/status/{status}")
    @Operation(summary = "Get deliveries by status", description = "Get all deliveries with a specific status")
    public ResponseEntity<List<DeliveryTracking>> getDeliveriesByStatus(@PathVariable String status) {
        try {
            DeliveryTracking.DeliveryStatus deliveryStatus = DeliveryTracking.DeliveryStatus.valueOf(status.toUpperCase());
            List<DeliveryTracking> deliveries = deliveryTrackingService.getDeliveriesByStatus(deliveryStatus);
            return ResponseEntity.ok(deliveries);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/deliveries/overdue")
    @Operation(summary = "Get overdue deliveries", description = "Get deliveries that are past their estimated delivery date")
    public ResponseEntity<List<DeliveryTracking>> getOverdueDeliveries() {
        List<DeliveryTracking> deliveries = deliveryTrackingService.getOverdueDeliveries();
        return ResponseEntity.ok(deliveries);
    }

    @GetMapping("/products/available")
    @Operation(summary = "Get available products", description = "Get all available products for delivery")
    public ResponseEntity<List<Product>> getAvailableProducts() {
        List<Product> products = productService.getProductsByStatus(Product.ProductStatus.AVAILABLE);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/dashboard/stats")
    @Operation(summary = "Get user dashboard stats", description = "Get delivery statistics for user dashboard")
    public ResponseEntity<Map<String, Object>> getUserDashboardStats(Authentication authentication) {
        String username = authentication.getName();
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalAssignedDeliveries", deliveryTrackingService.getDeliveryCountByUser(username));
        stats.put("pendingDeliveries", deliveryTrackingService.getDeliveriesByStatusAndUser(
            DeliveryTracking.DeliveryStatus.PENDING, username).size());
        stats.put("processingDeliveries", deliveryTrackingService.getDeliveriesByStatusAndUser(
            DeliveryTracking.DeliveryStatus.PROCESSING, username).size());
        stats.put("shippedDeliveries", deliveryTrackingService.getDeliveriesByStatusAndUser(
            DeliveryTracking.DeliveryStatus.SHIPPED, username).size());
        stats.put("deliveredDeliveries", deliveryTrackingService.getDeliveriesByStatusAndUser(
            DeliveryTracking.DeliveryStatus.DELIVERED, username).size());
        stats.put("overdueDeliveries", deliveryTrackingService.getOverdueDeliveries().size());
        
        return ResponseEntity.ok(stats);
    }
}
