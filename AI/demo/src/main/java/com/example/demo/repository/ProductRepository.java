package com.example.demo.repository;

import com.example.demo.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * Find product by SKU
     */
    Optional<Product> findBySku(String sku);
    
    /**
     * Check if SKU exists
     */
    boolean existsBySku(String sku);
    
    /**
     * Find products by category
     */
    List<Product> findByCategory(String category);
    
    /**
     * Find products by status
     */
    List<Product> findByStatus(Product.ProductStatus status);
    
    /**
     * Find products by category and status
     */
    List<Product> findByCategoryAndStatus(String category, Product.ProductStatus status);
    
    /**
     * Find products with quantity less than or equal to specified value
     */
    List<Product> findByQuantityLessThanEqual(Integer quantity);
    
    /**
     * Find products with quantity greater than specified value
     */
    List<Product> findByQuantityGreaterThan(Integer quantity);
    
    /**
     * Find products by price range
     */
    List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Find products created by specific user
     */
    List<Product> findByCreatedBy(String createdBy);
    
    /**
     * Find products by warehouse location
     */
    List<Product> findByWarehouseLocation(String warehouseLocation);
    
    /**
     * Search products by name, description, or SKU
     */
    @Query("SELECT p FROM Product p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.sku) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.category) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Product> searchProducts(@Param("searchTerm") String searchTerm);
    
    /**
     * Find all distinct categories
     */
    @Query("SELECT DISTINCT p.category FROM Product p ORDER BY p.category")
    List<String> findAllCategories();
    
    /**
     * Find all distinct warehouse locations
     */
    @Query("SELECT DISTINCT p.warehouseLocation FROM Product p WHERE p.warehouseLocation IS NOT NULL ORDER BY p.warehouseLocation")
    List<String> findAllWarehouseLocations();
    
    /**
     * Get low stock products (quantity <= threshold)
     */
    @Query("SELECT p FROM Product p WHERE p.quantity <= :threshold AND p.status = 'AVAILABLE'")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold);
    
    /**
     * Get total value of inventory
     */
    @Query("SELECT SUM(p.price * p.quantity) FROM Product p WHERE p.status = 'AVAILABLE'")
    BigDecimal getTotalInventoryValue();
    
    /**
     * Count products by status
     */
    long countByStatus(Product.ProductStatus status);
}
