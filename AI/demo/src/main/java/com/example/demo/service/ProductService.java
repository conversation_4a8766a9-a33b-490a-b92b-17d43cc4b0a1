package com.example.demo.service;

import com.example.demo.entity.Product;
import com.example.demo.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    public List<Product> getAllProducts() {
        return productRepository.findAll();
    }

    public Optional<Product> getProductById(Long id) {
        return productRepository.findById(id);
    }

    public Optional<Product> getProductBySku(String sku) {
        return productRepository.findBySku(sku);
    }

    public Product createProduct(Product product, String createdBy) {
        // Check if SKU already exists
        if (productRepository.existsBySku(product.getSku())) {
            throw new RuntimeException("Product with SKU " + product.getSku() + " already exists");
        }

        product.setCreatedBy(createdBy);
        return productRepository.save(product);
    }

    public Product updateProduct(Long id, Product productDetails, String updatedBy) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));

        // Check if SKU is being changed and if it already exists
        if (!product.getSku().equals(productDetails.getSku()) && 
            productRepository.existsBySku(productDetails.getSku())) {
            throw new RuntimeException("Product with SKU " + productDetails.getSku() + " already exists");
        }

        product.setName(productDetails.getName());
        product.setDescription(productDetails.getDescription());
        product.setSku(productDetails.getSku());
        product.setPrice(productDetails.getPrice());
        product.setQuantity(productDetails.getQuantity());
        product.setWeight(productDetails.getWeight());
        product.setDimensions(productDetails.getDimensions());
        product.setCategory(productDetails.getCategory());
        product.setStatus(productDetails.getStatus());
        product.setWarehouseLocation(productDetails.getWarehouseLocation());

        return productRepository.save(product);
    }

    public void deleteProduct(Long id) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));
        productRepository.delete(product);
    }

    public List<Product> getProductsByCategory(String category) {
        return productRepository.findByCategory(category);
    }

    public List<Product> getProductsByStatus(Product.ProductStatus status) {
        return productRepository.findByStatus(status);
    }

    public List<Product> searchProducts(String searchTerm) {
        return productRepository.searchProducts(searchTerm);
    }

    public List<Product> getLowStockProducts(Integer threshold) {
        return productRepository.findLowStockProducts(threshold);
    }

    public List<Product> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return productRepository.findByPriceBetween(minPrice, maxPrice);
    }

    public List<String> getAllCategories() {
        return productRepository.findAllCategories();
    }

    public List<String> getAllWarehouseLocations() {
        return productRepository.findAllWarehouseLocations();
    }

    public BigDecimal getTotalInventoryValue() {
        BigDecimal total = productRepository.getTotalInventoryValue();
        return total != null ? total : BigDecimal.ZERO;
    }

    public Product updateStock(Long id, Integer newQuantity) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));

        product.setQuantity(newQuantity);
        return productRepository.save(product);
    }

    public Product adjustStock(Long id, Integer adjustment) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));

        int newQuantity = product.getQuantity() + adjustment;
        if (newQuantity < 0) {
            throw new RuntimeException("Insufficient stock. Current quantity: " + product.getQuantity());
        }

        product.setQuantity(newQuantity);
        return productRepository.save(product);
    }

    public Product updateProductStatus(Long id, Product.ProductStatus status) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));

        product.setStatus(status);
        return productRepository.save(product);
    }

    public long getProductCountByStatus(Product.ProductStatus status) {
        return productRepository.countByStatus(status);
    }

    public List<Product> getProductsCreatedBy(String createdBy) {
        return productRepository.findByCreatedBy(createdBy);
    }

    public List<Product> getProductsByWarehouseLocation(String location) {
        return productRepository.findByWarehouseLocation(location);
    }
}
