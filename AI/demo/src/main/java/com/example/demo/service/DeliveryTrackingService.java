package com.example.demo.service;

import com.example.demo.entity.DeliveryTracking;
import com.example.demo.entity.Product;
import com.example.demo.repository.DeliveryTrackingRepository;
import com.example.demo.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class DeliveryTrackingService {

    @Autowired
    private DeliveryTrackingRepository deliveryTrackingRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProductService productService;

    public List<DeliveryTracking> getAllDeliveries() {
        return deliveryTrackingRepository.findAll();
    }

    public Optional<DeliveryTracking> getDeliveryById(Long id) {
        return deliveryTrackingRepository.findById(id);
    }

    public Optional<DeliveryTracking> getDeliveryByOrderId(String orderId) {
        return deliveryTrackingRepository.findByOrderId(orderId);
    }

    public Optional<DeliveryTracking> getDeliveryByTrackingNumber(String trackingNumber) {
        return deliveryTrackingRepository.findByTrackingNumber(trackingNumber);
    }

    public DeliveryTracking createDelivery(DeliveryTracking delivery, String assignedTo) {
        // Check if order ID already exists
        if (deliveryTrackingRepository.existsByOrderId(delivery.getOrderId())) {
            throw new RuntimeException("Delivery with order ID " + delivery.getOrderId() + " already exists");
        }

        // Validate product exists and has sufficient stock
        Product product = delivery.getProduct();
        if (product == null || product.getId() == null) {
            throw new RuntimeException("Product is required");
        }

        Product existingProduct = productRepository.findById(product.getId())
                .orElseThrow(() -> new RuntimeException("Product not found"));

        if (existingProduct.getQuantity() < delivery.getQuantity()) {
            throw new RuntimeException("Insufficient stock. Available: " + existingProduct.getQuantity() + 
                                     ", Requested: " + delivery.getQuantity());
        }

        // Reserve stock
        productService.adjustStock(existingProduct.getId(), -delivery.getQuantity());

        delivery.setProduct(existingProduct);
        delivery.setAssignedTo(assignedTo);
        delivery.setStatus(DeliveryTracking.DeliveryStatus.PENDING);

        return deliveryTrackingRepository.save(delivery);
    }

    public DeliveryTracking updateDeliveryStatus(Long id, DeliveryTracking.DeliveryStatus status, String updatedBy) {
        DeliveryTracking delivery = deliveryTrackingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Delivery not found with id: " + id));

        DeliveryTracking.DeliveryStatus oldStatus = delivery.getStatus();
        delivery.setStatus(status);

        // Handle stock adjustments for cancelled/returned orders
        if (status == DeliveryTracking.DeliveryStatus.CANCELLED || 
            status == DeliveryTracking.DeliveryStatus.RETURNED) {
            // Return stock to inventory
            productService.adjustStock(delivery.getProduct().getId(), delivery.getQuantity());
        }

        return deliveryTrackingRepository.save(delivery);
    }

    public DeliveryTracking updateDelivery(Long id, DeliveryTracking deliveryDetails, String updatedBy) {
        DeliveryTracking delivery = deliveryTrackingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Delivery not found with id: " + id));

        // Check if order ID is being changed and if it already exists
        if (!delivery.getOrderId().equals(deliveryDetails.getOrderId()) && 
            deliveryTrackingRepository.existsByOrderId(deliveryDetails.getOrderId())) {
            throw new RuntimeException("Delivery with order ID " + deliveryDetails.getOrderId() + " already exists");
        }

        delivery.setOrderId(deliveryDetails.getOrderId());
        delivery.setCustomerName(deliveryDetails.getCustomerName());
        delivery.setCustomerEmail(deliveryDetails.getCustomerEmail());
        delivery.setCustomerPhone(deliveryDetails.getCustomerPhone());
        delivery.setShippingAddress(deliveryDetails.getShippingAddress());
        delivery.setCarrierName(deliveryDetails.getCarrierName());
        delivery.setEstimatedDelivery(deliveryDetails.getEstimatedDelivery());
        delivery.setNotes(deliveryDetails.getNotes());

        return deliveryTrackingRepository.save(delivery);
    }

    public DeliveryTracking assignTrackingNumber(Long id, String trackingNumber) {
        DeliveryTracking delivery = deliveryTrackingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Delivery not found with id: " + id));

        delivery.setTrackingNumber(trackingNumber);
        return deliveryTrackingRepository.save(delivery);
    }

    public DeliveryTracking generateTrackingNumber(Long id) {
        String trackingNumber = "TRK" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return assignTrackingNumber(id, trackingNumber);
    }

    public List<DeliveryTracking> getDeliveriesByStatus(DeliveryTracking.DeliveryStatus status) {
        return deliveryTrackingRepository.findByStatus(status);
    }

    public List<DeliveryTracking> getDeliveriesAssignedTo(String assignedTo) {
        return deliveryTrackingRepository.findByAssignedTo(assignedTo);
    }

    public List<DeliveryTracking> getDeliveriesByStatusAndUser(DeliveryTracking.DeliveryStatus status, String assignedTo) {
        return deliveryTrackingRepository.findByStatusAndAssignedTo(status, assignedTo);
    }

    public List<DeliveryTracking> searchDeliveries(String searchTerm) {
        return deliveryTrackingRepository.searchDeliveries(searchTerm);
    }

    public List<DeliveryTracking> getOverdueDeliveries() {
        return deliveryTrackingRepository.findOverdueDeliveries(LocalDateTime.now());
    }

    public List<DeliveryTracking> getRecentDeliveries(int days) {
        LocalDateTime sinceDate = LocalDateTime.now().minusDays(days);
        return deliveryTrackingRepository.findRecentDeliveries(sinceDate);
    }

    public List<DeliveryTracking> getDeliveriesNeedingAttention(int hoursThreshold) {
        LocalDateTime thresholdDate = LocalDateTime.now().minusHours(hoursThreshold);
        return deliveryTrackingRepository.findDeliveriesNeedingAttention(thresholdDate);
    }

    public DeliveryTracking assignDelivery(Long id, String assignedTo) {
        DeliveryTracking delivery = deliveryTrackingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Delivery not found with id: " + id));

        delivery.setAssignedTo(assignedTo);
        return deliveryTrackingRepository.save(delivery);
    }

    public long getDeliveryCountByStatus(DeliveryTracking.DeliveryStatus status) {
        return deliveryTrackingRepository.countByStatus(status);
    }

    public long getDeliveryCountByUser(String assignedTo) {
        return deliveryTrackingRepository.countByAssignedTo(assignedTo);
    }

    public void deleteDelivery(Long id) {
        DeliveryTracking delivery = deliveryTrackingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Delivery not found with id: " + id));

        // Return stock if delivery is not completed
        if (delivery.getStatus() != DeliveryTracking.DeliveryStatus.DELIVERED &&
            delivery.getStatus() != DeliveryTracking.DeliveryStatus.CANCELLED &&
            delivery.getStatus() != DeliveryTracking.DeliveryStatus.RETURNED) {
            productService.adjustStock(delivery.getProduct().getId(), delivery.getQuantity());
        }

        deliveryTrackingRepository.delete(delivery);
    }
}
