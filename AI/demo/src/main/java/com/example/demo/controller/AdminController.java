package com.example.demo.controller;

import com.example.demo.entity.Product;
import com.example.demo.entity.User;
import com.example.demo.service.ProductService;
import com.example.demo.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Admin", description = "Admin operations for warehouse management")
public class AdminController {

    @Autowired
    private ProductService productService;

    @Autowired
    private AuthService authService;

    @GetMapping("/products")
    @Operation(summary = "Get all products", description = "Retrieve all products in the warehouse")
    public ResponseEntity<List<Product>> getAllProducts() {
        List<Product> products = productService.getAllProducts();
        return ResponseEntity.ok(products);
    }

    @GetMapping("/products/{id}")
    @Operation(summary = "Get product by ID", description = "Retrieve a specific product by its ID")
    public ResponseEntity<?> getProductById(@PathVariable Long id) {
        return productService.getProductById(id)
                .map(product -> ResponseEntity.ok().body(product))
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/products")
    @Operation(summary = "Create new product", description = "Add a new product to the warehouse")
    public ResponseEntity<?> createProduct(@Valid @RequestBody Product product, Authentication authentication) {
        try {
            String username = authentication.getName();
            Product createdProduct = productService.createProduct(product, username);
            return ResponseEntity.ok(createdProduct);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to create product: " + e.getMessage());
        }
    }

    @PutMapping("/products/{id}")
    @Operation(summary = "Update product", description = "Update an existing product")
    public ResponseEntity<?> updateProduct(@PathVariable Long id, @Valid @RequestBody Product productDetails, 
                                         Authentication authentication) {
        try {
            String username = authentication.getName();
            Product updatedProduct = productService.updateProduct(id, productDetails, username);
            return ResponseEntity.ok(updatedProduct);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to update product: " + e.getMessage());
        }
    }

    @DeleteMapping("/products/{id}")
    @Operation(summary = "Delete product", description = "Remove a product from the warehouse")
    public ResponseEntity<?> deleteProduct(@PathVariable Long id) {
        try {
            productService.deleteProduct(id);
            return ResponseEntity.ok("Product deleted successfully");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to delete product: " + e.getMessage());
        }
    }

    @PutMapping("/products/{id}/stock")
    @Operation(summary = "Update product stock", description = "Update the stock quantity of a product")
    public ResponseEntity<?> updateStock(@PathVariable Long id, @RequestBody Map<String, Integer> request) {
        try {
            Integer newQuantity = request.get("quantity");
            if (newQuantity == null || newQuantity < 0) {
                return ResponseEntity.badRequest().body("Invalid quantity");
            }
            
            Product updatedProduct = productService.updateStock(id, newQuantity);
            return ResponseEntity.ok(updatedProduct);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to update stock: " + e.getMessage());
        }
    }

    @PutMapping("/products/{id}/status")
    @Operation(summary = "Update product status", description = "Update the status of a product")
    public ResponseEntity<?> updateProductStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String statusStr = request.get("status");
            Product.ProductStatus status = Product.ProductStatus.valueOf(statusStr.toUpperCase());
            
            Product updatedProduct = productService.updateProductStatus(id, status);
            return ResponseEntity.ok(updatedProduct);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to update product status: " + e.getMessage());
        }
    }

    @GetMapping("/products/search")
    @Operation(summary = "Search products", description = "Search products by name, description, SKU, or category")
    public ResponseEntity<List<Product>> searchProducts(@RequestParam String query) {
        List<Product> products = productService.searchProducts(query);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/products/low-stock")
    @Operation(summary = "Get low stock products", description = "Get products with stock below threshold")
    public ResponseEntity<List<Product>> getLowStockProducts(@RequestParam(defaultValue = "10") Integer threshold) {
        List<Product> products = productService.getLowStockProducts(threshold);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/products/categories")
    @Operation(summary = "Get all categories", description = "Get all product categories")
    public ResponseEntity<List<String>> getAllCategories() {
        List<String> categories = productService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/dashboard/stats")
    @Operation(summary = "Get dashboard statistics", description = "Get warehouse statistics for admin dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalProducts", productService.getAllProducts().size());
        stats.put("availableProducts", productService.getProductCountByStatus(Product.ProductStatus.AVAILABLE));
        stats.put("outOfStockProducts", productService.getProductCountByStatus(Product.ProductStatus.OUT_OF_STOCK));
        stats.put("totalInventoryValue", productService.getTotalInventoryValue());
        stats.put("lowStockProducts", productService.getLowStockProducts(10).size());
        stats.put("categories", productService.getAllCategories().size());
        stats.put("warehouseLocations", productService.getAllWarehouseLocations().size());
        
        return ResponseEntity.ok(stats);
    }
}
