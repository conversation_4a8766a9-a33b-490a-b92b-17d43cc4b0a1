package com.example.demo.repository;

import com.example.demo.entity.DeliveryTracking;
import com.example.demo.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DeliveryTrackingRepository extends JpaRepository<DeliveryTracking, Long> {
    
    /**
     * Find delivery by order ID
     */
    Optional<DeliveryTracking> findByOrderId(String orderId);
    
    /**
     * Check if order ID exists
     */
    boolean existsByOrderId(String orderId);
    
    /**
     * Find delivery by tracking number
     */
    Optional<DeliveryTracking> findByTrackingNumber(String trackingNumber);
    
    /**
     * Find deliveries by status
     */
    List<DeliveryTracking> findByStatus(DeliveryTracking.DeliveryStatus status);
    
    /**
     * Find deliveries assigned to specific user
     */
    List<DeliveryTracking> findByAssignedTo(String assignedTo);
    
    /**
     * Find deliveries by status and assigned user
     */
    List<DeliveryTracking> findByStatusAndAssignedTo(DeliveryTracking.DeliveryStatus status, String assignedTo);
    
    /**
     * Find deliveries by product
     */
    List<DeliveryTracking> findByProduct(Product product);
    
    /**
     * Find deliveries by customer name
     */
    List<DeliveryTracking> findByCustomerNameContainingIgnoreCase(String customerName);
    
    /**
     * Find deliveries by customer email
     */
    List<DeliveryTracking> findByCustomerEmail(String customerEmail);
    
    /**
     * Find deliveries by carrier
     */
    List<DeliveryTracking> findByCarrierName(String carrierName);
    
    /**
     * Find deliveries created between dates
     */
    List<DeliveryTracking> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find deliveries with estimated delivery between dates
     */
    List<DeliveryTracking> findByEstimatedDeliveryBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find overdue deliveries (estimated delivery passed but not delivered)
     */
    @Query("SELECT d FROM DeliveryTracking d WHERE " +
           "d.estimatedDelivery < :currentDate AND " +
           "d.status NOT IN ('DELIVERED', 'CANCELLED', 'RETURNED')")
    List<DeliveryTracking> findOverdueDeliveries(@Param("currentDate") LocalDateTime currentDate);
    
    /**
     * Search deliveries by order ID, tracking number, or customer name
     */
    @Query("SELECT d FROM DeliveryTracking d WHERE " +
           "LOWER(d.orderId) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.trackingNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.customerName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.customerEmail) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<DeliveryTracking> searchDeliveries(@Param("searchTerm") String searchTerm);
    
    /**
     * Find deliveries by multiple statuses
     */
    List<DeliveryTracking> findByStatusIn(List<DeliveryTracking.DeliveryStatus> statuses);
    
    /**
     * Count deliveries by status
     */
    long countByStatus(DeliveryTracking.DeliveryStatus status);
    
    /**
     * Count deliveries assigned to user
     */
    long countByAssignedTo(String assignedTo);
    
    /**
     * Find recent deliveries (last N days)
     */
    @Query("SELECT d FROM DeliveryTracking d WHERE d.createdAt >= :sinceDate ORDER BY d.createdAt DESC")
    List<DeliveryTracking> findRecentDeliveries(@Param("sinceDate") LocalDateTime sinceDate);
    
    /**
     * Find deliveries that need attention (pending or processing for too long)
     */
    @Query("SELECT d FROM DeliveryTracking d WHERE " +
           "d.status IN ('PENDING', 'PROCESSING') AND " +
           "d.createdAt < :thresholdDate")
    List<DeliveryTracking> findDeliveriesNeedingAttention(@Param("thresholdDate") LocalDateTime thresholdDate);
}
